/* Mobile-First Registration Page Styles */

/* Ensure all touch targets are at least 44px */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Mobile form optimizations */
@media (max-width: 767px) {
  /* Form inputs */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="date"],
  select,
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    min-height: 44px;
    border-radius: 8px;
    border: 2px solid #d1d5db;
    transition: border-color 0.2s ease;
  }

  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="tel"]:focus,
  input[type="date"]:focus,
  select:focus,
  textarea:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Button optimizations */
  button,
  .btn {
    min-height: 44px;
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  /* Link optimizations */
  a {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    text-decoration: none;
    border-radius: 6px;
    transition: background-color 0.2s ease;
  }

  /* Form sections spacing */
  .form-section {
    margin-bottom: 32px;
  }

  /* Grid adjustments for mobile */
  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
  }

  /* Header adjustments */
  .mobile-header {
    padding: 16px;
  }

  .mobile-header h1 {
    font-size: 1.25rem;
  }

  /* Card padding adjustments */
  .mobile-card {
    padding: 20px;
    margin: 16px;
    border-radius: 12px;
  }

  /* Typography adjustments */
  .mobile-title {
    font-size: 1.75rem;
    line-height: 1.3;
    margin-bottom: 16px;
  }

  .mobile-subtitle {
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 24px;
  }

  /* Form labels */
  label {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 8px;
    display: block;
  }

  /* Error and success messages */
  .alert {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 24px;
    font-size: 14px;
    line-height: 1.5;
  }

  /* Loading states */
  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* File upload area */
  .file-upload {
    min-height: 120px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    transition: border-color 0.2s ease;
  }

  .file-upload:hover {
    border-color: #3b82f6;
  }

  /* Help section */
  .help-section {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 20px;
    margin-top: 24px;
  }

  /* Navigation improvements */
  .mobile-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 12px;
  }

  .mobile-nav a {
    font-size: 14px;
    padding: 8px 12px;
  }
}

/* Tablet optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .tablet-card {
    padding: 32px;
  }

  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="date"],
  select,
  textarea {
    font-size: 14px;
    padding: 10px 12px;
  }

  button,
  .btn {
    font-size: 14px;
    padding: 10px 20px;
  }
}

/* Desktop optimizations */
@media (min-width: 1025px) {
  .desktop-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 32px;
  }

  .desktop-card {
    padding: 40px;
  }

  /* Restore compact desktop spacing */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="date"],
  select,
  textarea {
    font-size: 14px;
    padding: 8px 12px;
    min-height: auto;
  }

  button,
  .btn {
    font-size: 14px;
    padding: 8px 16px;
    min-height: auto;
  }

  .form-section {
    margin-bottom: 24px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="date"],
  select,
  textarea {
    border-width: 2px;
  }

  button,
  .btn {
    border: 2px solid currentColor;
  }
}

/* Focus visible improvements */
*:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}
