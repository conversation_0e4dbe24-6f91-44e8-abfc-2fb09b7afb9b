'use client';

import { useSession } from 'next-auth/react';
import { signOut } from 'next-auth/react';
import Link from 'next/link';
import { FileText, User, Shield, LogOut, Home } from 'lucide-react';

export default function AuthTestPage() {
  const { data: session, status } = useSession();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex items-center mb-6">
            <FileText className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Authentication Test</h1>
              <p className="text-gray-600">IELTS Certification System</p>
            </div>
          </div>

          <div className="space-y-6">
            {/* Session Status */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-3 flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Session Status
              </h2>
              <div className="space-y-2">
                <p><strong>Status:</strong> <span className={`px-2 py-1 rounded text-sm ${
                  status === 'loading' ? 'bg-yellow-100 text-yellow-800' :
                  status === 'authenticated' ? 'bg-green-100 text-green-800' :
                  'bg-red-100 text-red-800'
                }`}>{status}</span></p>
                {session && (
                  <>
                    <p><strong>User ID:</strong> {session.user?.id || 'N/A'}</p>
                    <p><strong>Email:</strong> {session.user?.email || 'N/A'}</p>
                    <p><strong>Name:</strong> {session.user?.name || 'N/A'}</p>
                    <p><strong>Role:</strong> <span className={`px-2 py-1 rounded text-sm ${
                      session.user?.role === 'admin' ? 'bg-blue-100 text-blue-800' :
                      session.user?.role === 'test_checker' ? 'bg-green-100 text-green-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>{session.user?.role || 'N/A'}</span></p>
                  </>
                )}
              </div>
            </div>

            {/* Navigation Options */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-3">Navigation Options</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Link
                  href="/"
                  className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Home Page
                </Link>
                
                <Link
                  href="/auth/signin"
                  className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <User className="h-4 w-4 mr-2" />
                  Sign In
                </Link>

                {session?.user?.role === 'admin' && (
                  <Link
                    href="/admin"
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    Admin Dashboard
                  </Link>
                )}

                {session && (
                  <Link
                    href="/dashboard"
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    <User className="h-4 w-4 mr-2" />
                    Test Checker Dashboard
                  </Link>
                )}

                {session && (
                  <button
                    onClick={() => signOut({ callbackUrl: '/' })}
                    className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </button>
                )}
              </div>
            </div>

            {/* Debug Information */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold mb-3">Debug Information</h2>
              <div className="bg-gray-800 text-green-400 p-3 rounded font-mono text-sm overflow-auto">
                <pre>{JSON.stringify({ status, session }, null, 2)}</pre>
              </div>
            </div>

            {/* Demo Credentials */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h2 className="text-lg font-semibold mb-3 text-blue-800">Demo Credentials</h2>
              <div className="space-y-2 text-sm">
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>Test Checker:</strong> <EMAIL> / checker123</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
