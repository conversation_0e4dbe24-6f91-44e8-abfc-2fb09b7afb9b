#!/usr/bin/env node

/**
 * Test Authentication Flow Script
 * 
 * This script helps test the authentication flow locally and on Vercel
 * to ensure the login redirects work correctly for both admin and test checker users.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔐 IELTS Certification System - Authentication Flow Test');
console.log('=' .repeat(60));

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Read package.json to verify project
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
if (!packageJson.name || !packageJson.name.includes('ielts')) {
  console.error('❌ Error: This doesn\'t appear to be the IELTS Certification System project.');
  process.exit(1);
}

console.log('✅ Project verified: IELTS Certification System');
console.log('');

// Test 1: Build the project
console.log('🔨 Test 1: Building the project...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ Build successful');
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
console.log('');

// Test 2: Check environment variables
console.log('🔧 Test 2: Checking environment variables...');
const requiredEnvVars = [
  'NEXTAUTH_SECRET',
  'DATABASE_URL'
];

const envFile = path.join(process.cwd(), '.env.local');
let envVars = {};

if (fs.existsSync(envFile)) {
  const envContent = fs.readFileSync(envFile, 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      envVars[key.trim()] = value.trim();
    }
  });
}

let missingVars = [];
requiredEnvVars.forEach(varName => {
  if (!envVars[varName] && !process.env[varName]) {
    missingVars.push(varName);
  }
});

if (missingVars.length > 0) {
  console.log('⚠️  Missing environment variables:', missingVars.join(', '));
  console.log('   These are required for authentication to work properly.');
} else {
  console.log('✅ All required environment variables are set');
}
console.log('');

// Test 3: Check authentication configuration
console.log('🔐 Test 3: Checking authentication configuration...');
const authFilePath = path.join(process.cwd(), 'src', 'lib', 'auth.ts');
if (fs.existsSync(authFilePath)) {
  const authContent = fs.readFileSync(authFilePath, 'utf8');
  
  // Check for demo credentials
  if (authContent.includes('<EMAIL>') && authContent.includes('<EMAIL>')) {
    console.log('✅ Demo credentials found in auth configuration');
  } else {
    console.log('⚠️  Demo credentials not found in auth configuration');
  }
  
  // Check for trustHost setting
  if (authContent.includes('trustHost: true')) {
    console.log('✅ trustHost setting enabled for Vercel compatibility');
  } else {
    console.log('⚠️  trustHost setting not found - may cause issues on Vercel');
  }
} else {
  console.log('❌ Authentication configuration file not found');
}
console.log('');

// Test 4: Check middleware configuration
console.log('🛡️  Test 4: Checking middleware configuration...');
const middlewarePath = path.join(process.cwd(), 'src', 'middleware.ts');
if (fs.existsSync(middlewarePath)) {
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
  
  if (middlewareContent.includes('/admin') && middlewareContent.includes('/dashboard')) {
    console.log('✅ Protected routes configured in middleware');
  } else {
    console.log('⚠️  Protected routes not properly configured in middleware');
  }
} else {
  console.log('❌ Middleware configuration file not found');
}
console.log('');

// Test 5: Check dashboard layouts
console.log('📱 Test 5: Checking dashboard layouts...');
const adminLayoutPath = path.join(process.cwd(), 'src', 'app', 'admin', 'layout.tsx');
const dashboardLayoutPath = path.join(process.cwd(), 'src', 'app', 'dashboard', 'layout.tsx');

if (fs.existsSync(adminLayoutPath) && fs.existsSync(dashboardLayoutPath)) {
  console.log('✅ Dashboard layout files found');
} else {
  console.log('❌ Dashboard layout files missing');
}
console.log('');

// Summary and recommendations
console.log('📋 Summary and Recommendations:');
console.log('=' .repeat(60));
console.log('');

console.log('🔗 Test URLs (after deployment):');
console.log('   • Authentication Test: /auth/test');
console.log('   • Sign In: /auth/signin');
console.log('   • Admin Dashboard: /admin');
console.log('   • Test Checker Dashboard: /dashboard');
console.log('');

console.log('🔑 Demo Credentials:');
console.log('   • Admin: <EMAIL> / admin123');
console.log('   • Test Checker: <EMAIL> / checker123');
console.log('');

console.log('🚀 Deployment Steps:');
console.log('   1. Ensure all environment variables are set in Vercel');
console.log('   2. Deploy using: npx vercel --prod');
console.log('   3. Test authentication flow using /auth/test page');
console.log('   4. Verify dashboard access for both user types');
console.log('');

console.log('🔧 If authentication fails on Vercel:');
console.log('   1. Check Vercel function logs for errors');
console.log('   2. Verify NEXTAUTH_URL matches your domain');
console.log('   3. Ensure NEXTAUTH_SECRET is properly set');
console.log('   4. Check database connectivity');
console.log('');

console.log('✅ Authentication flow test completed!');
