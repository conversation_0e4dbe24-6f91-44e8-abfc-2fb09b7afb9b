import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { testResults, candidates, testRegistrations, aiFeedback, users } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { generateAIFeedback } from '@/lib/ai-service';
import { generateCertificate } from '@/lib/certificate-generator';
import { generateCertificateSerial } from '@/lib/utils/certificate';

// Helper function to auto-generate AI feedback and certificate
async function autoGenerateAIFeedbackAndCertificate(resultId: string) {
  try {
    console.log(`Auto-generating AI feedback and certificate for result: ${resultId}`);

    // Get test result with candidate info using new schema structure
    const result = await db
      .select({
        testResult: testResults,
        candidate: candidates,
        testRegistration: testRegistrations,
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(testResults.id, resultId))
      .limit(1);

    if (!result.length) {
      console.error('Test result not found for auto-generation:', resultId);
      return;
    }

    const { testResult, candidate } = result[0];

    // Generate AI Feedback if not already generated
    if (!testResult.aiFeedbackGenerated) {
      try {
        console.log('Generating AI feedback...');
        const feedbackData = await generateAIFeedback(testResult, candidate);

        // Save the feedback to database
        await db
          .insert(aiFeedback)
          .values({
            testResultId: resultId,
            listeningFeedback: feedbackData.listeningFeedback,
            readingFeedback: feedbackData.readingFeedback,
            writingFeedback: feedbackData.writingFeedback,
            speakingFeedback: feedbackData.speakingFeedback,
            overallFeedback: feedbackData.overallFeedback,
            studyRecommendations: feedbackData.recommendations,
            strengths: feedbackData.strengths,
            weaknesses: feedbackData.weaknesses,
            studyPlan: feedbackData.studyPlan,
          });

        console.log('AI feedback generated successfully');
      } catch (error) {
        console.error('Error generating AI feedback:', error);
      }
    }

    // Generate Certificate if not already generated
    if (!testResult.certificateGenerated) {
      try {
        console.log('Generating certificate...');

        // Generate certificate serial if not exists
        let certificateSerial = testResult.certificateSerial;
        if (!certificateSerial) {
          certificateSerial = generateCertificateSerial();
        }

        // Generate the certificate PDF
        await generateCertificate(testResult, candidate);

        console.log('Certificate generated successfully');
      } catch (error) {
        console.error('Error generating certificate:', error);
      }
    }

    // Update the test result to mark both as generated
    const updateData: Record<string, unknown> = {
      updatedAt: new Date()
    };

    if (!testResult.aiFeedbackGenerated) {
      updateData.aiFeedbackGenerated = true;
    }

    if (!testResult.certificateGenerated) {
      updateData.certificateGenerated = true;
      if (!testResult.certificateSerial) {
        updateData.certificateSerial = generateCertificateSerial();
      }
    }

    await db
      .update(testResults)
      .set(updateData)
      .where(eq(testResults.id, resultId));

    console.log('Auto-generation completed for result:', resultId);

  } catch (error) {
    console.error('Error in auto-generation process:', error);
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const data = await request.json();

    // Validate required fields
    if (!data.testRegistrationId) {
      return NextResponse.json(
        { error: 'Test Registration ID is required' },
        { status: 400 }
      );
    }

    // Check if test registration exists
    const testRegistration = await db
      .select()
      .from(testRegistrations)
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(eq(testRegistrations.id, data.testRegistrationId))
      .limit(1);

    if (!testRegistration.length) {
      return NextResponse.json(
        { error: 'Test registration not found' },
        { status: 404 }
      );
    }

    // Check if results already exist for this test registration
    const existingResults = await db
      .select()
      .from(testResults)
      .where(eq(testResults.testRegistrationId, data.testRegistrationId))
      .limit(1);

    const isUpdate = existingResults.length > 0;

    // Check if the user exists in database for foreign key constraint
    let enteredByUserId = null;
    if (session.user?.id) {
      try {
        const userExists = await db
          .select({ id: users.id })
          .from(users)
          .where(eq(users.id, session.user.id))
          .limit(1);

        if (userExists.length > 0) {
          enteredByUserId = session.user.id;
        } else {
          console.warn(`User ID ${session.user.id} not found in database, setting enteredBy to null`);
        }
      } catch (error) {
        console.error('Error checking user existence:', error);
        // Continue without setting enteredBy to avoid foreign key constraint violation
      }
    }

    // Convert string values to numbers where needed
    const processedData = {
      testRegistrationId: data.testRegistrationId,

      // Listening scores (keep as strings for decimal fields)
      listeningScore: data.listeningScore || null,
      listeningBandScore: data.listeningBandScore || null,

      // Reading scores
      readingScore: data.readingScore || null,
      readingBandScore: data.readingBandScore || null,

      // Writing scores
      writingTask1Score: data.writingTask1Score || null,
      writingTask2Score: data.writingTask2Score || null,
      writingBandScore: data.writingBandScore || null,

      // Speaking scores
      speakingFluencyScore: data.speakingFluencyScore || null,
      speakingLexicalScore: data.speakingLexicalScore || null,
      speakingGrammarScore: data.speakingGrammarScore || null,
      speakingPronunciationScore: data.speakingPronunciationScore || null,
      speakingBandScore: data.speakingBandScore || null,

      // Overall score
      overallBandScore: data.overallBandScore || null,

      // Metadata
      status: data.status || 'pending' as const,
      enteredBy: enteredByUserId, // ✅ FIXED: Only set if user exists in database
      updatedAt: new Date(),
    };

    let result;
    if (isUpdate) {
      // Update existing result
      result = await db
        .update(testResults)
        .set(processedData)
        .where(eq(testResults.id, existingResults[0].id))
        .returning();
    } else {
      // Create new test result
      result = await db
        .insert(testResults)
        .values(processedData)
        .returning();
    }

    const savedResult = result[0];

    // Auto-generate AI feedback and certificate if result is completed
    if (processedData.status === 'completed' && savedResult.overallBandScore) {
      // Run in background without blocking the response
      setImmediate(async () => {
        try {
          await autoGenerateAIFeedbackAndCertificate(savedResult.id);
        } catch (error) {
          console.error('Background generation failed:', error);
        }
      });
    }

    return NextResponse.json(savedResult, { status: isUpdate ? 200 : 201 });
  } catch (error) {
    console.error('Error creating test result:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');

    const offset = (page - 1) * limit;
    const userId = session.user?.id;

    // Build where conditions
    const baseCondition = eq(testResults.enteredBy, userId);
    const whereConditions = status && status !== 'all'
      ? and(baseCondition, eq(testResults.status, status as 'pending' | 'completed' | 'verified'))
      : baseCondition;

    // Get results with candidate info using new schema structure
    const results = await db
      .select({
        id: testResults.id,
        testRegistrationId: testResults.testRegistrationId,
        listeningBandScore: testResults.listeningBandScore,
        readingBandScore: testResults.readingBandScore,
        writingBandScore: testResults.writingBandScore,
        speakingBandScore: testResults.speakingBandScore,
        overallBandScore: testResults.overallBandScore,
        status: testResults.status,
        createdAt: testResults.createdAt,
        candidate: {
          id: candidates.id,
          fullName: candidates.fullName,
          passportNumber: candidates.passportNumber,
        },
        testRegistration: {
          candidateNumber: testRegistrations.candidateNumber,
          testDate: testRegistrations.testDate,
          testCenter: testRegistrations.testCenter,
        },
      })
      .from(testResults)
      .innerJoin(testRegistrations, eq(testResults.testRegistrationId, testRegistrations.id))
      .innerJoin(candidates, eq(testRegistrations.candidateId, candidates.id))
      .where(whereConditions)
      .limit(limit)
      .offset(offset)
      .orderBy(testResults.createdAt);

    return NextResponse.json({
      results,
      page,
      limit,
    });
  } catch (error) {
    console.error('Error fetching test results:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
