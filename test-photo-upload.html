<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Upload Test - IELTS Certification System</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #1f2937;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 10px 10px 0;
            font-size: 14px;
            transition: background 0.2s;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button.success {
            background: #10b981;
        }
        .test-button.error {
            background: #ef4444;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .result.success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .result.error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .file-input {
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            width: 100%;
        }
        .mobile-test {
            background: #eff6ff;
            border: 1px solid #3b82f6;
        }
        .responsive-info {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #0ea5e9;
        }
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .test-container {
                padding: 20px;
            }
            .test-button {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">📱 IELTS Mobile Responsiveness & Photo Upload Test</h1>
        
        <div class="responsive-info">
            <h3>📋 Mobile Responsiveness Test Instructions</h3>
            <p><strong>Resize your browser window or use browser dev tools to test different screen sizes:</strong></p>
            <ul>
                <li><strong>Mobile:</strong> &lt; 768px width</li>
                <li><strong>Tablet:</strong> 768px - 1024px width</li>
                <li><strong>Desktop:</strong> &gt; 1024px width</li>
            </ul>
            <p><strong>Test the following pages:</strong></p>
            <ul>
                <li><a href="http://localhost:3002" target="_blank">Home Page</a></li>
                <li><a href="http://localhost:3002/search" target="_blank">Search Page</a></li>
                <li><a href="http://localhost:3002/register" target="_blank">Registration Page</a></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 Photo Upload API Test (Registration - No Auth Required)</h3>
            <p>This test simulates photo upload during candidate registration (should work without authentication):</p>
            <input type="file" id="registrationPhoto" class="file-input" accept="image/*">
            <button class="test-button" onclick="testRegistrationUpload()">Test Registration Photo Upload</button>
            <div id="registrationResult"></div>
        </div>

        <div class="test-section">
            <h3>🔒 Photo Upload API Test (Admin - Auth Required)</h3>
            <p>This test simulates photo upload in admin interface (should require authentication):</p>
            <input type="file" id="adminPhoto" class="file-input" accept="image/*">
            <button class="test-button" onclick="testAdminUpload()">Test Admin Photo Upload</button>
            <div id="adminResult"></div>
        </div>

        <div class="test-section mobile-test">
            <h3>📱 Mobile Touch Target Test</h3>
            <p>Test that all buttons meet the 44px minimum touch target requirement:</p>
            <button class="test-button" onclick="testTouchTarget(this)">Touch Target Test (44px min)</button>
            <button class="test-button" onclick="testTouchTarget(this)">Another Touch Target</button>
            <button class="test-button" onclick="testTouchTarget(this)">Third Touch Target</button>
            <div id="touchResult"></div>
        </div>

        <div class="test-section">
            <h3>🌐 Navigation Test</h3>
            <p>Test navigation functionality across different screen sizes:</p>
            <button class="test-button" onclick="testNavigation()">Test Navigation Links</button>
            <div id="navResult"></div>
        </div>
    </div>

    <script>
        async function testRegistrationUpload() {
            const fileInput = document.getElementById('registrationPhoto');
            const resultDiv = document.getElementById('registrationResult');
            
            if (!fileInput.files[0]) {
                showResult(resultDiv, 'Please select a file first', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('type', 'photo');
            formData.append('isRegistration', 'true');

            try {
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (response.ok) {
                    showResult(resultDiv, `✅ SUCCESS: Registration photo upload worked!\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ ERROR: ${result.error}\nStatus: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ NETWORK ERROR: ${error.message}`, 'error');
            }
        }

        async function testAdminUpload() {
            const fileInput = document.getElementById('adminPhoto');
            const resultDiv = document.getElementById('adminResult');
            
            if (!fileInput.files[0]) {
                showResult(resultDiv, 'Please select a file first', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('type', 'photo');
            // Note: isRegistration is false/undefined, so auth is required

            try {
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (response.status === 401) {
                    showResult(resultDiv, `✅ SUCCESS: Admin upload correctly requires authentication!\nError: ${result.error}`, 'success');
                } else if (response.ok) {
                    showResult(resultDiv, `✅ SUCCESS: Admin upload worked (user is authenticated)\n${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ UNEXPECTED ERROR: ${result.error}\nStatus: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ NETWORK ERROR: ${error.message}`, 'error');
            }
        }

        function testTouchTarget(button) {
            const rect = button.getBoundingClientRect();
            const resultDiv = document.getElementById('touchResult');
            const minSize = 44;
            
            const width = rect.width;
            const height = rect.height;
            const meetsRequirement = width >= minSize && height >= minSize;
            
            const message = `Button dimensions: ${width.toFixed(1)}px × ${height.toFixed(1)}px\nMinimum required: ${minSize}px × ${minSize}px\nMeets requirement: ${meetsRequirement ? '✅ YES' : '❌ NO'}`;
            
            showResult(resultDiv, message, meetsRequirement ? 'success' : 'error');
        }

        function testNavigation() {
            const resultDiv = document.getElementById('navResult');
            const screenWidth = window.innerWidth;
            
            let deviceType;
            if (screenWidth < 768) {
                deviceType = 'Mobile';
            } else if (screenWidth < 1024) {
                deviceType = 'Tablet';
            } else {
                deviceType = 'Desktop';
            }
            
            const message = `Current screen width: ${screenWidth}px\nDevice type: ${deviceType}\n\nNavigation should be:\n${screenWidth < 768 ? '📱 Hamburger menu (mobile)' : '🖥️ Horizontal navigation (tablet/desktop)'}`;
            
            showResult(resultDiv, message, 'success');
        }

        function showResult(element, message, type) {
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        // Auto-detect screen size changes
        window.addEventListener('resize', () => {
            const width = window.innerWidth;
            console.log(`Screen width changed to: ${width}px`);
        });

        // Initial load message
        window.addEventListener('load', () => {
            console.log('📱 Mobile Responsiveness & Photo Upload Test Page Loaded');
            console.log('🔧 Open browser dev tools and test different screen sizes');
            console.log('📋 Test the navigation, touch targets, and photo upload functionality');
        });
    </script>
</body>
</html>
