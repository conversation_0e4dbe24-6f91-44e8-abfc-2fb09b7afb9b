'use client';

import { Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircle,
  Calendar,
  FileText,
  Mail,
  Phone,
  Home
} from 'lucide-react';

function SuccessContent() {
  const searchParams = useSearchParams();
  const candidateNumber = searchParams.get('candidateNumber');
  const testDate = searchParams.get('testDate');

  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4 md:py-6">
            <div className="flex items-center">
              <FileText className="h-6 w-6 md:h-8 md:w-8 text-green-600 mr-2 md:mr-3" />
              <h1 className="text-lg md:text-2xl font-bold text-gray-900">Registration Complete</h1>
            </div>
            <Link
              href="/"
              className="flex items-center text-gray-600 hover:text-gray-900 text-sm md:text-base"
            >
              <Home className="h-4 w-4 mr-1" />
              Home
            </Link>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 md:py-12">
        {/* Success Message */}
        <div className="text-center mb-8 md:mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 md:w-24 md:h-24 bg-green-100 rounded-full mb-6">
            <CheckCircle className="h-10 w-10 md:h-12 md:w-12 text-green-600" />
          </div>
          <h2 className="text-2xl md:text-4xl font-bold text-gray-900 mb-4">
            Registration Successful!
          </h2>
          <p className="text-gray-600 text-sm md:text-lg max-w-2xl mx-auto">
            Your IELTS test registration has been completed successfully. Please save your registration details below.
          </p>
        </div>

        {/* Registration Details */}
        <div className="bg-white rounded-xl shadow-xl p-6 md:p-8 border border-gray-100 mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-600" />
            Your Registration Details
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {candidateNumber && (
              <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center mb-2">
                  <FileText className="h-5 w-5 text-blue-600 mr-2" />
                  <span className="text-sm font-medium text-blue-900">Candidate Number</span>
                </div>
                <p className="text-2xl font-bold text-blue-900">#{candidateNumber}</p>
                <p className="text-xs text-blue-700 mt-1">Keep this number for your records</p>
              </div>
            )}

            {testDate && (
              <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                <div className="flex items-center mb-2">
                  <Calendar className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-sm font-medium text-green-900">Test Date</span>
                </div>
                <p className="text-lg font-semibold text-green-900">{formatDate(testDate)}</p>
                <p className="text-xs text-green-700 mt-1">Mark your calendar</p>
              </div>
            )}
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-white rounded-xl shadow-xl p-6 md:p-8 border border-gray-100 mb-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">What Happens Next?</h3>
          
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <span className="text-sm font-semibold text-blue-600">1</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Confirmation Email</h4>
                <p className="text-sm text-gray-600 mt-1">
                  You will receive a confirmation email with your registration details and test instructions.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <span className="text-sm font-semibold text-blue-600">2</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Test Preparation</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Prepare for your test using official IELTS materials and practice tests.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <span className="text-sm font-semibold text-blue-600">3</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Test Day</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Arrive at the test center 30 minutes early with your identification document.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                <span className="text-sm font-semibold text-blue-600">4</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Results</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Your results will be available 13 days after your test date. You can search for them on this website.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Important Information */}
        <div className="bg-amber-50 rounded-xl p-6 md:p-8 border border-amber-200 mb-8">
          <h3 className="text-lg font-semibold text-amber-900 mb-4">Important Information</h3>
          <div className="text-sm text-amber-800 space-y-2">
            <p>• Bring the same identification document you used for registration</p>
            <p>• Arrive at the test center at least 30 minutes before your test time</p>
            <p>• No electronic devices are allowed in the test room</p>
            <p>• Contact the test center if you need to make any changes to your registration</p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row justify-center space-y-3 sm:space-y-0 sm:space-x-4">
          <Link
            href="/search"
            className="inline-flex items-center justify-center px-6 py-3 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-white hover:bg-blue-50 transition-colors"
            style={{ minHeight: '44px' }}
          >
            <FileText className="h-4 w-4 mr-2" />
            Search Results (After Test)
          </Link>
          
          <Link
            href="/"
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            style={{ minHeight: '44px' }}
          >
            <Home className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
        </div>

        {/* Contact Information */}
        <div className="mt-8 text-center">
          <p className="text-gray-600 text-sm mb-4">
            Need help or have questions about your registration?
          </p>
          <div className="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-600">
            <div className="flex items-center">
              <Mail className="h-4 w-4 mr-1" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center">
              <Phone className="h-4 w-4 mr-1" />
              <span>+44 ************</span>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export default function RegistrationSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <SuccessContent />
    </Suspense>
  );
}
