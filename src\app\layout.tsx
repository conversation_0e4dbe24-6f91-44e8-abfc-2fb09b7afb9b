import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import AuthSessionProvider from "@/components/providers/session-provider";
import "./globals.css";
import "../styles/mobile-responsive.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "IELTS Certification System",
  description: "Professional IELTS test result management and certification system",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthSessionProvider>
          {children}
        </AuthSessionProvider>
      </body>
    </html>
  );
}
