/* Mobile-First Responsive Design for IELTS Certification System */

/* Base mobile styles (default) */
* {
  box-sizing: border-box;
}

/* Touch-friendly base styles */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Mobile Navigation */
.mobile-nav-toggle {
  display: none;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s ease;
}

.mobile-nav-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.mobile-nav-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 50;
  padding: 8px;
}

.mobile-nav-menu.open {
  display: block;
}

.mobile-nav-item {
  display: block;
  padding: 12px 16px;
  text-decoration: none;
  color: #374151;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.mobile-nav-item:hover {
  background-color: #f3f4f6;
}

/* Mobile-specific styles (< 768px) */
@media (max-width: 767px) {
  /* Show mobile navigation toggle */
  .mobile-nav-toggle {
    display: block;
  }

  /* Hide desktop navigation */
  .desktop-nav {
    display: none;
  }

  /* Header adjustments */
  .mobile-header {
    padding: 12px 16px;
  }

  .mobile-header h1 {
    font-size: 1.25rem;
    line-height: 1.3;
  }

  /* Hero section mobile optimization */
  .hero-section {
    padding: 32px 16px;
    text-align: center;
  }

  .hero-title {
    font-size: 2rem;
    line-height: 1.2;
    margin-bottom: 16px;
  }

  .hero-subtitle {
    font-size: 1rem;
    line-height: 1.5;
    margin-bottom: 32px;
  }

  /* Card layouts */
  .mobile-card {
    margin: 12px;
    padding: 20px;
    border-radius: 12px;
  }

  /* Form optimizations */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="search"],
  select,
  textarea,
  button {
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 44px;
    padding: 12px 16px;
    border-radius: 8px;
    border: 2px solid #d1d5db;
    transition: border-color 0.2s ease;
  }

  input:focus,
  select:focus,
  textarea:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  /* Button optimizations */
  button,
  .btn {
    min-height: 44px;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
  }

  /* Link optimizations */
  a {
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    padding: 8px 12px;
    text-decoration: none;
    border-radius: 6px;
    transition: background-color 0.2s ease;
  }

  /* Search page mobile optimizations */
  .search-form {
    padding: 20px;
  }

  .search-input-container {
    margin-bottom: 20px;
  }

  .search-results {
    padding: 16px;
  }

  /* Convert table to card layout on mobile */
  .results-table {
    display: none;
  }

  .results-cards {
    display: block;
  }

  .result-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .result-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }

  .result-card-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
  }

  .result-card-info {
    flex: 1;
  }

  .result-card-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 4px;
  }

  .result-card-details {
    font-size: 0.875rem;
    color: #6b7280;
  }

  .result-card-scores {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 16px;
  }

  .score-item {
    text-align: center;
    padding: 8px;
    background: #f9fafb;
    border-radius: 6px;
  }

  .score-label {
    font-size: 0.75rem;
    color: #6b7280;
    margin-bottom: 4px;
  }

  .score-value {
    font-weight: 600;
    font-size: 1.1rem;
  }

  /* Horizontal navigation mobile optimization */
  .horizontal-nav {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .horizontal-nav::-webkit-scrollbar {
    display: none;
  }

  .horizontal-nav-container {
    display: flex;
    gap: 8px;
    padding: 16px;
    min-width: max-content;
  }

  .horizontal-nav-item {
    flex-shrink: 0;
    min-width: 140px;
    padding: 12px 16px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e5e7eb;
    background: white;
    transition: all 0.2s ease;
  }

  .horizontal-nav-item.active {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
  }

  /* Results page mobile layout */
  .results-layout {
    display: block;
  }

  .results-sidebar {
    margin-bottom: 24px;
  }

  .results-main {
    width: 100%;
  }

  /* Candidate info card mobile */
  .candidate-info-mobile {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
  }

  .candidate-photo-mobile {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-bottom: 16px;
  }

  /* Score cards mobile layout */
  .score-cards-mobile {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 24px;
  }

  .score-card-mobile {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
  }

  /* Chart containers mobile */
  .chart-container-mobile {
    width: 100%;
    height: 250px;
    margin-bottom: 24px;
  }

  /* File upload mobile optimization */
  .file-upload-mobile {
    min-height: 120px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.2s ease;
  }

  .file-upload-mobile:hover {
    border-color: #3b82f6;
  }

  /* Spacing adjustments */
  .mobile-spacing {
    padding: 16px;
  }

  .mobile-section {
    margin-bottom: 24px;
  }

  /* Typography mobile */
  .mobile-title {
    font-size: 1.5rem;
    line-height: 1.3;
    margin-bottom: 12px;
  }

  .mobile-subtitle {
    font-size: 0.875rem;
    line-height: 1.5;
    color: #6b7280;
    margin-bottom: 20px;
  }
}

/* Tablet styles (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  /* Show desktop navigation, hide mobile toggle */
  .mobile-nav-toggle {
    display: none;
  }

  .desktop-nav {
    display: flex;
  }

  /* Tablet grid layouts */
  .tablet-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .tablet-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }

  /* Tablet card layouts */
  .tablet-card {
    padding: 24px;
  }

  /* Form adjustments for tablet */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="search"],
  select,
  textarea {
    font-size: 14px;
    padding: 10px 12px;
    min-height: 40px;
  }

  button,
  .btn {
    font-size: 14px;
    padding: 10px 20px;
    min-height: 40px;
  }

  /* Results table shows on tablet */
  .results-table {
    display: table;
    width: 100%;
    overflow-x: auto;
  }

  .results-cards {
    display: none;
  }

  /* Horizontal navigation tablet */
  .horizontal-nav-container {
    gap: 12px;
    padding: 20px;
  }

  .horizontal-nav-item {
    min-width: 160px;
    padding: 14px 18px;
  }

  /* Results layout tablet */
  .results-layout {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 24px;
  }

  /* Score cards tablet */
  .score-cards-tablet {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
  }

  /* Chart containers tablet */
  .chart-container-tablet {
    height: 300px;
  }
}

/* Desktop styles (> 1024px) */
@media (min-width: 1025px) {
  /* Desktop navigation */
  .mobile-nav-toggle {
    display: none;
  }

  .desktop-nav {
    display: flex;
  }

  /* Desktop grid layouts */
  .desktop-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
  }

  .desktop-grid-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }

  /* Desktop card layouts */
  .desktop-card {
    padding: 32px;
  }

  /* Compact desktop form elements */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="search"],
  select,
  textarea {
    font-size: 14px;
    padding: 8px 12px;
    min-height: auto;
  }

  button,
  .btn {
    font-size: 14px;
    padding: 8px 16px;
    min-height: auto;
  }

  /* Desktop results layout */
  .results-layout {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 32px;
  }

  /* Desktop horizontal navigation */
  .horizontal-nav-container {
    gap: 16px;
    padding: 24px;
  }

  .horizontal-nav-item {
    min-width: 180px;
    padding: 16px 20px;
  }

  /* Desktop score cards */
  .score-cards-desktop {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }

  /* Desktop chart containers */
  .chart-container-desktop {
    height: 400px;
  }

  /* Compact desktop spacing */
  .desktop-spacing {
    padding: 24px;
  }

  .desktop-section {
    margin-bottom: 32px;
  }
}

/* Accessibility and performance optimizations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

@media (prefers-contrast: high) {
  input,
  select,
  textarea,
  button {
    border-width: 2px;
  }
}

/* Focus improvements */
*:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .mobile-nav-toggle,
  .mobile-nav-menu {
    display: none !important;
  }
}
