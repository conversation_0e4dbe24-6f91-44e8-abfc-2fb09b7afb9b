'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Search, FileText, ArrowLeft, Eye, User } from 'lucide-react';

interface SearchResult {
  id: string;
  listeningBandScore?: number;
  readingBandScore?: number;
  writingBandScore?: number;
  speakingBandScore?: number;
  overallBandScore?: number;
  certificateGenerated: boolean;
  candidate: {
    fullName: string;
    testDate: string;
    testCenter: string;
  };
}

export default function SearchPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [identificationType, setIdentificationType] = useState('passport'); // For user clarity only
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    setHasSearched(true);

    try {
      const response = await fetch('/api/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery.trim(),
        }),
      });

      if (response.ok) {
        const results = await response.json();
        setSearchResults(results);
      } else {
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4 lg:py-6 mobile-header">
            <div className="flex items-center">
              <Link href="/" className="flex items-center text-blue-600 hover:text-blue-700 mr-2 lg:mr-4 touch-target">
                <ArrowLeft className="h-4 w-4 lg:h-5 lg:w-5 mr-1" />
                <span className="hidden sm:inline">Back</span>
              </Link>
              <FileText className="h-6 w-6 lg:h-8 lg:w-8 text-blue-600 mr-2 lg:mr-3" />
              <h1 className="text-lg lg:text-2xl font-bold text-gray-900">Search IELTS Results</h1>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12 mobile-spacing">
        {/* Search Form */}
        <div className="bg-white rounded-xl shadow-xl p-6 lg:p-8 mb-6 lg:mb-8 border border-gray-100 mobile-card search-form">
          <div className="text-center mb-6 lg:mb-8">
            <div className="inline-flex items-center justify-center w-12 h-12 lg:w-16 lg:h-16 bg-blue-100 rounded-full mb-3 lg:mb-4">
              <Search className="h-6 w-6 lg:h-8 lg:w-8 text-blue-600" />
            </div>
            <h2 className="text-xl lg:text-3xl font-bold text-gray-900 mb-2 mobile-title">Find Your IELTS Results</h2>
            <p className="text-sm lg:text-base text-gray-600 mobile-subtitle">Enter your identification number to find your results</p>
          </div>

          <form onSubmit={handleSearch} className="space-y-4 lg:space-y-6">
            <div>
              <label htmlFor="identificationType" className="block text-sm font-medium text-gray-700 mb-2 lg:mb-3">
                Identification Type
              </label>
              <select
                id="identificationType"
                value={identificationType}
                onChange={(e) => setIdentificationType(e.target.value)}
                className="w-full px-3 lg:px-4 py-3 text-base lg:text-lg border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 touch-target"
              >
                <option value="passport">Passport Number</option>
                <option value="birth_certificate">Birth Certificate Number</option>
              </select>
            </div>

            <div className="relative">
              <label htmlFor="searchQuery" className="block text-sm font-medium text-gray-700 mb-3">
                {identificationType === 'passport' ? 'Passport Number' : 'Birth Certificate Number'}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  id="searchQuery"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={`Enter your ${identificationType === 'passport' ? 'passport number' : 'birth certificate number'}`}
                  className="w-full pl-12 pr-4 py-4 text-lg border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  required
                />
              </div>
              <div className="mt-2 text-xs text-gray-500">
                <span className="font-medium">Examples:</span> {identificationType === 'passport' ? 'A12345678' : 'BC123456789'}
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center items-center px-6 py-4 border border-transparent text-lg font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                  Searching...
                </>
              ) : (
                <>
                  <Search className="h-5 w-5 mr-3" />
                  Search Results
                </>
              )}
            </button>
          </form>
        </div>

        {/* Search Results */}
        {hasSearched && (
          <div className="bg-white rounded-xl shadow-xl p-8 border border-gray-100">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-gray-900">Search Results</h3>
              {searchResults.length > 0 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  {searchResults.length} result{searchResults.length !== 1 ? 's' : ''} found
                </span>
              )}
            </div>

            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-lg text-gray-600">Searching for your results...</p>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="space-y-4 lg:space-y-6 results-cards">
                {searchResults.map((result) => (
                  <div key={result.id} className="border border-gray-200 rounded-xl p-4 lg:p-6 hover:shadow-lg transition-shadow duration-200 bg-gradient-to-r from-gray-50 to-white result-card">
                    <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start">
                      <div className="flex-1">
                        <div className="flex items-center mb-3 result-card-header">
                          <div className="w-10 h-10 lg:w-12 lg:h-12 bg-blue-100 rounded-full flex items-center justify-center mr-3 lg:mr-4 result-card-avatar">
                            <User className="h-5 w-5 lg:h-6 lg:w-6 text-blue-600" />
                          </div>
                          <div className="result-card-info">
                            <h4 className="text-lg lg:text-xl font-bold text-gray-900 result-card-name">{result.candidate.fullName}</h4>
                            <p className="text-sm lg:text-base text-gray-600 result-card-details">Test Date: {new Date(result.candidate.testDate).toLocaleDateString()}</p>
                          </div>
                        </div>

                        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-600 mb-1">
                            <span className="font-medium">Test Center:</span> {result.candidate.testCenter}
                          </p>
                        </div>

                        {/* Module Scores */}
                        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-4 mb-4 result-card-scores">
                          {result.listeningBandScore && (
                            <div className="text-center p-2 lg:p-3 bg-blue-50 rounded-lg score-item">
                              <p className="text-xs lg:text-sm font-medium text-blue-800 score-label">Listening</p>
                              <p className="text-lg lg:text-2xl font-bold text-blue-600 score-value">{result.listeningBandScore}</p>
                            </div>
                          )}
                          {result.readingBandScore && (
                            <div className="text-center p-2 lg:p-3 bg-green-50 rounded-lg score-item">
                              <p className="text-xs lg:text-sm font-medium text-green-800 score-label">Reading</p>
                              <p className="text-lg lg:text-2xl font-bold text-green-600 score-value">{result.readingBandScore}</p>
                            </div>
                          )}
                          {result.writingBandScore && (
                            <div className="text-center p-2 lg:p-3 bg-yellow-50 rounded-lg score-item">
                              <p className="text-xs lg:text-sm font-medium text-yellow-800 score-label">Writing</p>
                              <p className="text-lg lg:text-2xl font-bold text-yellow-600 score-value">{result.writingBandScore}</p>
                            </div>
                          )}
                          {result.speakingBandScore && (
                            <div className="text-center p-2 lg:p-3 bg-purple-50 rounded-lg score-item">
                              <p className="text-xs lg:text-sm font-medium text-purple-800 score-label">Speaking</p>
                              <p className="text-lg lg:text-2xl font-bold text-purple-600 score-value">{result.speakingBandScore}</p>
                            </div>
                          )}
                        </div>

                        {/* Overall Score */}
                        {result.overallBandScore && (
                          <div className="text-center p-3 lg:p-4 bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg mb-4 lg:mb-0">
                            <p className="text-xs lg:text-sm font-medium text-gray-700 mb-1">Overall Band Score</p>
                            <p className="text-2xl lg:text-4xl font-bold text-indigo-600">{result.overallBandScore}</p>
                          </div>
                        )}
                      </div>

                      <div className="mt-4 lg:mt-0 lg:ml-6 flex flex-col space-y-3">
                        <Link
                          href={`/results/${result.id}`}
                          className="inline-flex items-center justify-center px-4 lg:px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 shadow-lg hover:shadow-xl touch-target"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          <span className="hidden sm:inline">View Detailed Results</span>
                          <span className="sm:hidden">View Results</span>
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <Search className="h-12 w-12 text-gray-400" />
                </div>
                <h4 className="text-xl font-semibold text-gray-900 mb-2">No Results Found</h4>
                <p className="text-gray-600 mb-4">We could not find any test results matching your search criteria.</p>
                <div className="text-sm text-gray-500">
                  <p className="mb-2">Please check that you have entered:</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Your identification number exactly as registered</li>
                    <li>The correct identification type (passport or birth certificate)</li>
                    <li>No extra spaces or special characters</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  );
}
