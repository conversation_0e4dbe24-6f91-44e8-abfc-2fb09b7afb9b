import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string; // 'photo' or 'document'
    const candidateId = formData.get('candidateId') as string; // For photo uploads
    const isRegistration = formData.get('isRegistration') === 'true'; // For registration uploads

    // For non-registration uploads, require authentication
    if (!isRegistration) {
      const session = await auth();
      if (!session) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        );
      }
    }

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = {
      photo: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
      document: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png']
    };

    const validTypes = allowedTypes[type as keyof typeof allowedTypes] || allowedTypes.photo;

    if (!validTypes.includes(file.type)) {
      return NextResponse.json(
        { error: `Invalid file type. Allowed types: ${validTypes.join(', ')}` },
        { status: 400 }
      );
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 5MB' },
        { status: 400 }
      );
    }

    // Convert file to base64 for database storage
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const base64Data = `data:${file.type};base64,${buffer.toString('base64')}`;

    // If this is a photo upload for a candidate, store it in the database
    if (type === 'photo' && candidateId) {
      try {
        await db
          .update(candidates)
          .set({
            photoData: base64Data,
            photoUrl: `candidate-photo-${candidateId}`, // Keep a reference
            updatedAt: new Date()
          })
          .where(eq(candidates.id, candidateId));

        return NextResponse.json({
          success: true,
          url: `candidate-photo-${candidateId}`,
          fileName: file.name,
          originalName: file.name,
          size: file.size,
          type: file.type,
          storedInDatabase: true,
        });
      } catch (error) {
        console.error('Error storing photo in database:', error);
        return NextResponse.json(
          { error: 'Failed to store photo in database' },
          { status: 500 }
        );
      }
    }

    // For other file types, return base64 data directly
    return NextResponse.json({
      success: true,
      url: base64Data,
      fileName: file.name,
      originalName: file.name,
      size: file.size,
      type: file.type,
      storedInDatabase: false,
    });

  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}

// Handle file deletion
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const fileUrl = searchParams.get('url');
    const candidateId = searchParams.get('candidateId');

    if (!fileUrl) {
      return NextResponse.json(
        { error: 'File URL is required' },
        { status: 400 }
      );
    }

    // If this is a candidate photo stored in database
    if (fileUrl.startsWith('candidate-photo-') && candidateId) {
      try {
        await db
          .update(candidates)
          .set({
            photoData: null,
            photoUrl: null,
            updatedAt: new Date()
          })
          .where(eq(candidates.id, candidateId));

        return NextResponse.json({
          success: true,
          message: 'Photo deleted successfully from database',
        });
      } catch (error) {
        console.error('Error deleting photo from database:', error);
        return NextResponse.json(
          { error: 'Failed to delete photo from database' },
          { status: 500 }
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: 'File deletion handled',
    });

  } catch (error) {
    console.error('File deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete file' },
      { status: 500 }
    );
  }
}
