/**
 * Fix demo users in database
 * This script ensures the hardcoded demo user IDs exist in the database
 * to prevent foreign key constraint violations
 */

import { db } from '../src/lib/db';
import { users } from '../src/lib/db/schema';
import { eq } from 'drizzle-orm';
import bcrypt from 'bcryptjs';

async function fixDemoUsers() {
  try {
    console.log('🔧 Fixing demo users in database...');

    // Demo users with hardcoded IDs (matching auth.ts)
    const demoUsers = [
      {
        id: 'admin-1',
        email: '<EMAIL>',
        password: 'admin123',
        name: 'Admin User',
        role: 'admin' as const
      },
      {
        id: 'checker-1',
        email: '<EMAIL>',
        password: 'checker123',
        name: 'Test Checker',
        role: 'test_checker' as const
      }
    ];

    for (const demoUser of demoUsers) {
      console.log(`\n🔍 Checking user: ${demoUser.email} (ID: ${demoUser.id})`);

      // Check if user already exists with this ID
      const existingUserById = await db
        .select()
        .from(users)
        .where(eq(users.id, demoUser.id))
        .limit(1);

      // Check if user exists with this email
      const existingUserByEmail = await db
        .select()
        .from(users)
        .where(eq(users.email, demoUser.email))
        .limit(1);

      if (existingUserById.length > 0) {
        console.log(`✅ User with ID ${demoUser.id} already exists`);
        continue;
      }

      if (existingUserByEmail.length > 0) {
        // User exists with email but different ID - update the ID
        console.log(`🔄 Updating existing user ${demoUser.email} to use ID ${demoUser.id}`);
        
        await db
          .update(users)
          .set({ id: demoUser.id })
          .where(eq(users.email, demoUser.email));
        
        console.log(`✅ Updated user ${demoUser.email} to use ID ${demoUser.id}`);
      } else {
        // User doesn't exist - create new user with specific ID
        console.log(`➕ Creating new user: ${demoUser.email} with ID ${demoUser.id}`);
        
        const hashedPassword = await bcrypt.hash(demoUser.password, 10);
        
        await db
          .insert(users)
          .values({
            id: demoUser.id,
            name: demoUser.name,
            email: demoUser.email,
            password: hashedPassword,
            role: demoUser.role,
          });
        
        console.log(`✅ Created user ${demoUser.email} with ID ${demoUser.id}`);
      }
    }

    // Verify the users exist
    console.log('\n🔍 Verifying demo users...');
    for (const demoUser of demoUsers) {
      const user = await db
        .select()
        .from(users)
        .where(eq(users.id, demoUser.id))
        .limit(1);

      if (user.length > 0) {
        console.log(`✅ Verified: ${user[0].email} (ID: ${user[0].id}, Role: ${user[0].role})`);
      } else {
        console.log(`❌ Failed to verify: ${demoUser.email} (ID: ${demoUser.id})`);
      }
    }

    console.log('\n✅ Demo users fix completed!');
    console.log('\nDemo credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Test Checker: <EMAIL> / checker123');

  } catch (error) {
    console.error('❌ Demo users fix failed:', error);
    throw error;
  }
}

// Run the fix
fixDemoUsers()
  .then(() => {
    console.log('🎉 Demo users fix successful!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Demo users fix failed:', error);
    process.exit(1);
  });
