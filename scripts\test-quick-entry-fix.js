/**
 * Test script to verify the Quick Entry fix
 * Tests that candidate answers are properly saved to the database
 */

const BASE_URL = 'http://localhost:3000';

async function testQuickEntryFix() {
  console.log('🧪 Testing Quick Entry Fix - Candidate Answer Saving');
  console.log('=' .repeat(60));

  try {
    // Test 1: Check if candidates API returns registrationId
    console.log('\n1️⃣ Testing candidates API with registrationId...');
    const testDate = '2025-01-31'; // Use a recent test date
    const candidatesResponse = await fetch(`${BASE_URL}/api/checker/candidates?includeResults=true&testDate=${testDate}`);
    
    if (!candidatesResponse.ok) {
      console.log(`❌ Candidates API failed: ${candidatesResponse.status}`);
      return;
    }

    const candidatesData = await candidatesResponse.json();
    console.log(`✅ Candidates API working - found ${candidatesData.total} candidates`);
    
    if (candidatesData.candidates && candidatesData.candidates.length > 0) {
      const sampleCandidate = candidatesData.candidates[0];
      console.log(`   Sample candidate: ${sampleCandidate.fullName} (${sampleCandidate.candidateNumber})`);
      console.log(`   Has registrationId: ${!!sampleCandidate.registrationId}`);
      console.log(`   Registration ID: ${sampleCandidate.registrationId}`);
      console.log(`   Has existing result: ${sampleCandidate.hasResult}`);

      // Test 2: Try to save a test score using the correct API structure
      console.log('\n2️⃣ Testing score save with correct testRegistrationId...');
      
      const testScoreData = {
        testRegistrationId: sampleCandidate.registrationId, // ✅ Using correct field
        listeningBandScore: '7.5',
        readingBandScore: '8.0',
        writingTask1Score: '7.0',
        writingTask2Score: '7.5',
        writingBandScore: '7.25',
        speakingBandScore: '7.5',
        overallBandScore: '7.5',
        status: 'completed'
      };

      const saveUrl = sampleCandidate.hasResult 
        ? `${BASE_URL}/api/checker/results/${sampleCandidate.result?.id}`
        : `${BASE_URL}/api/checker/results`;
      
      const saveMethod = sampleCandidate.hasResult ? 'PUT' : 'POST';

      console.log(`   Using ${saveMethod} ${saveUrl}`);
      console.log(`   Test data:`, JSON.stringify(testScoreData, null, 2));

      const saveResponse = await fetch(saveUrl, {
        method: saveMethod,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testScoreData)
      });

      if (saveResponse.ok) {
        const savedData = await saveResponse.json();
        console.log(`✅ Score save successful!`);
        console.log(`   Result ID: ${savedData.id}`);
        console.log(`   Overall Band Score: ${savedData.overallBandScore}`);
        console.log(`   Status: ${savedData.status}`);
        
        // Test 3: Verify the data was actually saved by fetching it back
        console.log('\n3️⃣ Verifying data persistence...');
        const verifyResponse = await fetch(`${BASE_URL}/api/checker/candidates?includeResults=true&testDate=${testDate}`);
        
        if (verifyResponse.ok) {
          const verifyData = await verifyResponse.json();
          const updatedCandidate = verifyData.candidates.find(c => c.id === sampleCandidate.id);
          
          if (updatedCandidate && updatedCandidate.hasResult) {
            console.log(`✅ Data persistence verified!`);
            console.log(`   Updated candidate has result: ${updatedCandidate.hasResult}`);
            console.log(`   Listening: ${updatedCandidate.result?.listeningBandScore}`);
            console.log(`   Reading: ${updatedCandidate.result?.readingBandScore}`);
            console.log(`   Writing T1: ${updatedCandidate.result?.writingTask1Score}`);
            console.log(`   Writing T2: ${updatedCandidate.result?.writingTask2Score}`);
            console.log(`   Speaking: ${updatedCandidate.result?.speakingBandScore}`);
            console.log(`   Overall: ${updatedCandidate.result?.overallBandScore}`);
          } else {
            console.log(`❌ Data persistence failed - candidate result not found`);
          }
        } else {
          console.log(`❌ Verification failed: ${verifyResponse.status}`);
        }

      } else {
        const errorData = await saveResponse.json();
        console.log(`❌ Score save failed: ${saveResponse.status}`);
        console.log(`   Error: ${errorData.error}`);
        console.log(`   This indicates the fix may not be working properly`);
      }

    } else {
      console.log(`⚠️  No candidates found for test date ${testDate}`);
      console.log(`   You may need to add test candidates first`);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }

  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Quick Entry Fix Test Complete');
}

// Run the test
testQuickEntryFix();
