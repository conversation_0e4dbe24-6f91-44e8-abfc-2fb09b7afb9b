// Test script for the registration API
const testRegistration = async () => {
  const testData = {
    fullName: "<PERSON>",
    email: "<EMAIL>",
    phoneNumber: "+1234567890",
    dateOfBirth: "1990-01-01",
    nationality: "American",
    passportNumber: "A12345678",
    testDate: "2024-12-31",
    testCenter: "Innovative Centre - Samarkand"
  };

  try {
    console.log('Testing registration API...');
    console.log('Test data:', testData);

    const response = await fetch('http://localhost:3001/api/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const result = await response.json();
    
    console.log('Response status:', response.status);
    console.log('Response data:', result);

    if (response.ok) {
      console.log('✅ Registration test PASSED');
      console.log('Candidate Number:', result.registration.candidateNumber);
      console.log('Test Date:', result.registration.testDate);
    } else {
      console.log('❌ Registration test FAILED');
      console.log('Error:', result.error);
    }
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
};

// Run the test
testRegistration();
