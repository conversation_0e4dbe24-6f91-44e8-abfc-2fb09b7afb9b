# IELTS Certification System

A comprehensive IELTS test result management and certification system built with Next.js 15, TypeScript, and PostgreSQL.

## Features

### ✅ Completed Features
- **Modern Tech Stack**: Next.js 15, TypeScript, Tailwind CSS, Drizzle ORM
- **Authentication**: NextAuth.js with role-based access control
- **Database Schema**: Complete schema for users, candidates, test results, and AI feedback
- **Public Search**: Search functionality for test results
- **Admin Dashboard**: Candidate management and system overview
- **Test Checker Dashboard**: Result entry and management interface
- **Responsive Design**: Mobile-friendly interface

### 🚧 In Development
- AI feedback generation with Claude API or OpenAI API
- Certificate PDF generation
- File upload for candidate photos
- Advanced search and filtering
- Bulk operations
- Data export functionality

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Lucide React icons
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: NextAuth.js v5
- **AI Integration**: Anthropic Claude API
- **Deployment**: Vercel-ready

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ielts-certification-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your values:
   ```env
   # Core Application
   NEXTAUTH_SECRET=your-secret-key-here
   NEXTAUTH_URL=http://localhost:3000
   DATABASE_URL=your-postgresql-connection-string

   # AI Integration
   ANTHROPIC_API_KEY=your-claude-api-key

   # Admin Credentials (for demo)
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=admin123
   ```

4. **Set up the database**
   ```bash
   # Generate database schema
   npm run db:generate

   # Run migrations
   npm run db:migrate

   # Set up initial users
   npm run db:setup
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Demo Credentials

- **Admin**: <EMAIL> / admin123
- **Test Checker**: <EMAIL> / checker123

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── admin/             # Admin dashboard pages
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Test checker dashboard
│   ├── search/            # Public search page
│   └── api/               # API routes
├── lib/                   # Utility libraries
│   ├── db/               # Database configuration and schema
│   ├── auth.ts           # Authentication configuration
│   └── utils.ts          # Utility functions
└── components/           # Reusable React components
```

## Database Schema

The system uses a comprehensive PostgreSQL schema with the following main tables:

- **users**: System users (admin, test_checker)
- **candidates**: Test candidates with personal information
- **testResults**: IELTS test scores and results
- **aiFeedback**: AI-generated feedback and recommendations

## API Routes

### Public Routes
- `POST /api/search` - Search test results

### Admin Routes
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/candidates` - List candidates
- `POST /api/admin/candidates` - Create candidate

### Checker Routes
- `GET /api/checker/dashboard` - Checker dashboard stats

## Development

### Database Management

```bash
# Generate new migration
npm run db:generate

# Apply migrations
npm run db:migrate

# Open database studio
npm run db:studio
```

### Code Quality

```bash
# Run linting
npm run lint

# Type checking
npx tsc --noEmit
```

## Deployment

The application is optimized for Vercel deployment:

1. **Push to GitHub**
2. **Connect to Vercel**
3. **Set environment variables**
4. **Deploy**

### Environment Variables for Production

```env
NEXTAUTH_SECRET=your-production-secret
NEXTAUTH_URL=https://your-domain.vercel.app
DATABASE_URL=your-production-database-url
ANTHROPIC_API_KEY=your-claude-api-key
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
